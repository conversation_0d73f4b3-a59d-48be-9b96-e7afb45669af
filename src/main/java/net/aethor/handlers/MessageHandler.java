package net.aethor.handlers;

import net.aethor.config.ModConfig;

/**
 * <PERSON><PERSON> chịu trách nhiệm quản lý và hiển thị messages
 * Cung cấp các utility methods cho việc hiển thị thông báo
 */
public class MessageHandler {

    private static MessageHandler instance;

    private MessageHandler() {
        // Singleton constructor
    }

    /**
     * Singleton pattern
     */
    public static MessageHandler getInstance() {
        if (instance == null) {
            instance = new MessageHandler();
        }
        return instance;
    }

    /**
     * Hiển thị initialization message
     */
    public void showInitMessage(String featureName, boolean enabled) {
        if (ModConfig.DEBUG_MODE) {
            String status = enabled ? "ENABLED" : "DISABLED";
            System.out.println("[" + ModConfig.MOD_NAME + "] " + featureName + " feature: " + status);
        }
    }
}