package net.aethor.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;

/**
 * Quản lý việc lưu và load cấu hình mod từ file JSON
 */
public class ConfigManager {

    private static final String CONFIG_FILE_NAME = "quicksettingkey.json";
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    private static final Path CONFIG_DIR = FabricLoader.getInstance().getConfigDir();
    private static final File CONFIG_FILE = CONFIG_DIR.resolve(CONFIG_FILE_NAME).toFile();

    // Config instance
    private static ConfigData configData = new ConfigData();

    /**
     * Load cấu hình từ file JSON
     */
    public static void loadConfig() {
        if (!CONFIG_FILE.exists()) {
            // Tạo file config mặc định nếu chưa tồn tại
            saveConfig();
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Created default config file");
            }
            return;
        }

        try (FileReader reader = new FileReader(CONFIG_FILE)) {
            JsonObject jsonObject = JsonParser.parseReader(reader).getAsJsonObject();

            // Load brightness settings
            if (jsonObject.has("brightness")) {
                JsonObject brightness = jsonObject.getAsJsonObject("brightness");
                if (brightness.has("current_value")) {
                    configData.currentBrightness = brightness.get("current_value").getAsDouble();
                }
                if (brightness.has("enabled")) {
                    configData.brightnessEnabled = brightness.get("enabled").getAsBoolean();
                }
            }

            // Load FOV settings
            if (jsonObject.has("fov")) {
                JsonObject fov = jsonObject.getAsJsonObject("fov");
                if (fov.has("current_value")) {
                    configData.currentFOV = fov.get("current_value").getAsInt();
                }
            }

            // Load render distance settings
            if (jsonObject.has("render_distance")) {
                JsonObject renderDistance = jsonObject.getAsJsonObject("render_distance");
                if (renderDistance.has("current_value")) {
                    configData.currentRenderDistance = renderDistance.get("current_value").getAsInt();
                }
            }

            // Load simulation distance settings
            if (jsonObject.has("simulation_distance")) {
                JsonObject simDistance = jsonObject.getAsJsonObject("simulation_distance");
                if (simDistance.has("current_value")) {
                    configData.currentSimulationDistance = simDistance.get("current_value").getAsInt();
                }
            }

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Config loaded successfully");
                System.out.println("[" + ModConfig.MOD_NAME + "] Brightness: " + configData.currentBrightness);
            }

        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error loading config: " + e.getMessage());
            // Fallback to default values
            configData = new ConfigData();
        }
    }

    /**
     * Save cấu hình vào file JSON
     */
    public static void saveConfig() {
        try {
            // Tạo config directory nếu chưa tồn tại
            if (!CONFIG_DIR.toFile().exists()) {
                CONFIG_DIR.toFile().mkdirs();
            }

            JsonObject root = new JsonObject();

            // Brightness settings
            JsonObject brightness = new JsonObject();
            brightness.addProperty("current_value", configData.currentBrightness);
            brightness.addProperty("enabled", configData.brightnessEnabled);
            brightness.addProperty("min_value", ModConfig.BRIGHTNESS_MIN);
            brightness.addProperty("max_value", ModConfig.BRIGHTNESS_MAX);
            brightness.addProperty("step", ModConfig.BRIGHTNESS_STEP);
            root.add("brightness", brightness);

            // FOV settings
            JsonObject fov = new JsonObject();
            fov.addProperty("current_value", configData.currentFOV);
            fov.addProperty("min_value", ModConfig.MIN_FOV);
            fov.addProperty("max_value", ModConfig.MAX_FOV);
            fov.addProperty("step", ModConfig.DEFAULT_FOV_STEP);
            root.add("fov", fov);

            // Render distance settings
            JsonObject renderDistance = new JsonObject();
            renderDistance.addProperty("current_value", configData.currentRenderDistance);
            renderDistance.addProperty("min_value", ModConfig.MIN_RENDER_DISTANCE);
            renderDistance.addProperty("max_value", ModConfig.MAX_RENDER_DISTANCE);
            root.add("render_distance", renderDistance);

            // Simulation distance settings
            JsonObject simDistance = new JsonObject();
            simDistance.addProperty("current_value", configData.currentSimulationDistance);
            simDistance.addProperty("min_value", ModConfig.MIN_SIMULATION_DISTANCE);
            simDistance.addProperty("max_value", ModConfig.MAX_SIMULATION_DISTANCE);
            root.add("simulation_distance", simDistance);

            // Version info
            root.addProperty("config_version", "1.0");
            root.addProperty("mod_version", ModConfig.MOD_NAME);

            try (FileWriter writer = new FileWriter(CONFIG_FILE)) {
                GSON.toJson(root, writer);
            }

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Config saved successfully");
            }

        } catch (IOException e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error saving config: " + e.getMessage());
        }
    }

    // Getters cho brightness
    public static double getCurrentBrightness() {
        return configData.currentBrightness;
    }

    public static void setCurrentBrightness(double brightness) {
        configData.currentBrightness = Math.max(ModConfig.BRIGHTNESS_MIN,
                Math.min(ModConfig.BRIGHTNESS_MAX, brightness));
        saveConfig(); // Auto-save khi thay đổi
    }

    public static boolean isBrightnessEnabled() {
        return configData.brightnessEnabled;
    }

    public static void setBrightnessEnabled(boolean enabled) {
        configData.brightnessEnabled = enabled;
        saveConfig();
    }

    // Getters cho FOV
    public static int getCurrentFOV() {
        return configData.currentFOV;
    }

    public static void setCurrentFOV(int fov) {
        configData.currentFOV = ModConfig.clampFOV(fov);
        saveConfig();
    }

    // Getters cho render distance
    public static int getCurrentRenderDistance() {
        return configData.currentRenderDistance;
    }

    public static void setCurrentRenderDistance(int distance) {
        configData.currentRenderDistance = ModConfig.clampRenderDistance(distance);
        saveConfig();
    }

    // Getters cho simulation distance
    public static int getCurrentSimulationDistance() {
        return configData.currentSimulationDistance;
    }

    public static void setCurrentSimulationDistance(int distance) {
        configData.currentSimulationDistance = ModConfig.clampSimulationDistance(distance);
        saveConfig();
    }

    /**
     * Reset về cấu hình mặc định
     */
    public static void resetToDefaults() {
        configData = new ConfigData();
        saveConfig();
        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Config reset to defaults");
        }
    }

    /**
     * Class chứa dữ liệu cấu hình
     */
    private static class ConfigData {
        public double currentBrightness = 1.0; // Brightness mặc định
        public boolean brightnessEnabled = true;
        public int currentFOV = 70; // FOV mặc định
        public int currentRenderDistance = 12; // Render distance mặc định
        public int currentSimulationDistance = 12; // Simulation distance mặc định
    }
}