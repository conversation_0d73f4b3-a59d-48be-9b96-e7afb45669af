package net.aethor.features;

import net.aethor.config.ModConfig;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.sound.SoundCategory;
import net.minecraft.text.Text;

/**
 * Abstract base class cho các sound features
 * Chứa logic chung để tắt/bật âm thanh cho các loại sound categories khác nhau
 * Có 2 trạng thái: ON/OFF
 */
public abstract class AbstractSoundFeature {

    protected final KeyBinding toggleKey;
    protected boolean isSoundOn = true;
    
    // Lưu âm lượng gốc ban đầu của Minecraft (lấy khi khởi tạo)
    protected double minecraftOriginalVolume = 1.0;
    
    // Lưu âm lượng hiện tại trước khi tắt (để restore khi bật lại)
    protected double currentVolume = 1.0;
    
    // Lưu âm lượng cuối cùng để so s<PERSON>h thay đổi
    protected double lastKnownVolume = 1.0;
    
    // Flag để đảm bảo initialize chỉ được gọi một lần
    protected boolean isInitialized = false;

    public AbstractSoundFeature() {
        // Đăng ký key binding cho toggle sound
        this.toggleKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                getToggleKeyTranslation(),
                InputUtil.Type.KEYSYM,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                getKeyCategoryTranslation()
        ));
    }

    /**
     * Khởi tạo và lưu giá trị gốc ban đầu của Minecraft
     * Gọi method này sau khi client đã sẵn sàng (chỉ một lần)
     */
    public void initialize(MinecraftClient client) {
        if (!isInitialized && client != null && client.options != null) {
            // Lưu giá trị gốc ban đầu của Minecraft
            minecraftOriginalVolume = client.options.getSoundVolumeOption(getSoundCategory()).getValue();
            
            // Kiểm tra trạng thái ban đầu: nếu = 0 thì đang tắt
            boolean isOriginallyOff = (minecraftOriginalVolume == 0.0);
            
            if (isOriginallyOff) {
                // Nếu ban đầu đã tắt, set trạng thái là OFF và giá trị hiện tại là default restore volume để restore
                isSoundOn = false;
                currentVolume = getDefaultRestoreVolume();
            } else {
                // Nếu ban đầu đang bật, set trạng thái là ON và giá trị hiện tại bằng giá trị gốc
                isSoundOn = true;
                currentVolume = minecraftOriginalVolume;
            }
            
            // Khởi tạo giá trị cuối cùng để theo dõi thay đổi
            lastKnownVolume = minecraftOriginalVolume;
            
            isInitialized = true;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Initialized " + getFeatureName() + " - Original volume: " + 
                    minecraftOriginalVolume + ", Initial state: " + (isSoundOn ? "ON" : "OFF") + 
                    ", Default restore volume: " + getDefaultRestoreVolume());
            }
        }
    }

    /**
     * Xử lý input từ bàn phím cho sound feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Theo dõi thay đổi settings âm thanh từ người dùng
        checkForSoundSettingsChanges(client);

        // Xử lý phím toggle
        while (toggleKey.wasPressed()) {
            toggleSound(client);
        }
    }

    /**
     * Theo dõi thay đổi settings âm thanh từ người dùng
     */
    private void checkForSoundSettingsChanges(MinecraftClient client) {
        if (!isInitialized || client == null || client.options == null) return;

        double currentGameVolume = client.options.getSoundVolumeOption(getSoundCategory()).getValue();

        // Kiểm tra xem có thay đổi không
        boolean hasChanged = (currentGameVolume != lastKnownVolume);

        if (hasChanged) {
            // Cập nhật giá trị cuối cùng
            lastKnownVolume = currentGameVolume;

            // Phân tích trạng thái mới dựa trên giá trị âm thanh
            boolean newSoundState = !(currentGameVolume == 0.0);
            
            if (newSoundState != isSoundOn) {
                // Trạng thái đã thay đổi, cập nhật
                isSoundOn = newSoundState;
                
                if (isSoundOn) {
                    // Người dùng đã bật âm thanh, lưu giá trị hiện tại
                    currentVolume = currentGameVolume;
                } else {
                    // Người dùng đã tắt âm thanh, giữ nguyên giá trị restore
                    // (không thay đổi currentVolume)
                }

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User changed " + getFeatureName() + " settings - New state: " + 
                        (isSoundOn ? "ON" : "OFF") + ", Volume: " + currentGameVolume);
                }
            } else if (isSoundOn) {
                // Trạng thái không đổi nhưng đang bật, cập nhật giá trị hiện tại
                currentVolume = currentGameVolume;

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User adjusted " + getFeatureName() + " volume: " + currentVolume);
                }
            }
        }
    }

    /**
     * Toggle giữa ON/OFF sound
     */
    private void toggleSound(MinecraftClient client) {
        // Chuyển đổi trạng thái
        isSoundOn = !isSoundOn;
        
        if (isSoundOn) {
            // Bật âm thanh - restore âm lượng hiện tại
            setVolume(client, currentVolume);
            showStateMessage(client, true);
        } else {
            // Lưu âm lượng hiện tại trước khi tắt (chỉ khi không phải trường hợp đặc biệt)
            if (!(minecraftOriginalVolume == 0.0)) {
                currentVolume = client.options.getSoundVolumeOption(getSoundCategory()).getValue();
            }
            // Tắt âm thanh - set về 0
            setVolume(client, 0.0);
            showStateMessage(client, false);
        }

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] " + getFeatureName() + " state changed to: " + (isSoundOn ? "ON" : "OFF") +
                " (Current volume: " + currentVolume + ")");
        }
    }

    /**
     * Đặt âm lượng cho sound category
     */
    private void setVolume(MinecraftClient client, double volume) {
        client.options.getSoundVolumeOption(getSoundCategory()).setValue(volume);
    }

    /**
     * Hiển thị thông báo trạng thái hiện tại
     */
    private void showStateMessage(MinecraftClient client, boolean isOn) {
        if (client.player != null) {
            Text featureName = Text.translatable(getFeatureNameTranslation());
            Text statusText = Text.translatable(isOn ? getOnMessageTranslation() : getOffMessageTranslation());
            String color = isOn ? "§a" : "§c"; // Green for ON, Red for OFF
            
            client.player.sendMessage(
                Text.literal(featureName.getString() + "§7: " + color + statusText.getString()),
                true
            );
        }
    }

    /**
     * Reset sound về giá trị gốc ban đầu của Minecraft (dùng khi thoát game)
     */
    public void resetSound(MinecraftClient client) {
        if (client != null && client.options != null) {
            // Hoàn nguyên về giá trị gốc ban đầu của Minecraft
            setVolume(client, minecraftOriginalVolume);
            isSoundOn = true;
            
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] " + getFeatureName() + " reset to Minecraft original volume: " + 
                    minecraftOriginalVolume);
            }
        }
    }

    // Getters

    public KeyBinding getToggleKey() {
        return toggleKey;
    }

    public boolean isSoundOn() {
        return isSoundOn;
    }

    /**
     * Lấy giá trị gốc ban đầu của Minecraft
     */
    public double getMinecraftOriginalVolume() {
        return minecraftOriginalVolume;
    }

    // Abstract methods - phải được implement bởi subclass

    /**
     * Lấy SoundCategory mà feature này điều khiển
     */
    protected abstract SoundCategory getSoundCategory();

    /**
     * Lấy translation key cho toggle key binding
     */
    protected abstract String getToggleKeyTranslation();

    /**
     * Lấy translation key cho key category
     */
    protected abstract String getKeyCategoryTranslation();

    /**
     * Lấy translation key cho tên feature (hiển thị trong message)
     */
    protected abstract String getFeatureNameTranslation();

    /**
     * Lấy translation key cho message ON
     */
    protected abstract String getOnMessageTranslation();

    /**
     * Lấy translation key cho message OFF
     */
    protected abstract String getOffMessageTranslation();

    /**
     * Lấy tên feature (dùng cho logging/debug)
     */
    public abstract String getFeatureName();

    /**
     * Kiểm tra xem feature có được enable không
     */
    public abstract boolean isEnabled();

    /**
     * Lấy default restore volume khi âm lượng ban đầu = 0
     */
    protected abstract double getDefaultRestoreVolume();
}
