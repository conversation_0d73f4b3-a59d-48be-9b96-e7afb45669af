package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.text.Text;

import java.util.Timer;
import java.util.TimerTask;

/**
 * Abstract base class cho tất cả distance-related features
 * Cung cấp common functionality và interface chung với debouncing
 */
public abstract class AbstractDistanceFeature {

    protected final String featureName;
    protected final KeyBinding increaseKey;
    protected final KeyBinding decreaseKey;
    protected final int minValue;
    protected final int maxValue;
    protected final int step;

    // Debouncing variables - đơn giản
    private Timer debounceTimer;
    private int pendingValue;
    private int originalValue;
    private boolean hasPendingChange = false;

    public AbstractDistanceFeature(String featureName,
                                   String increaseKeyTranslation,
                                   String decreaseKeyTranslation,
                                   String category,
                                   int increaseKeyCode,
                                   int decreaseKeyCode,
                                   int minValue,
                                   int maxValue,
                                   int step) {
        this.featureName = featureName;
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.step = step;

        // Đăng ký key bindings
        this.increaseKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                increaseKeyTranslation,
                InputUtil.Type.KEYSYM,
                increaseKeyCode,
                category
        ));

        this.decreaseKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                decreaseKeyTranslation,
                InputUtil.Type.KEYSYM,
                decreaseKeyCode,
                category
        ));
    }

    /**
     * Xử lý input từ bàn phím cho feature này
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Xử lý phím tăng
        while (increaseKey.wasPressed()) {
            handleIncrease(client);
        }

        // Xử lý phím giảm
        while (decreaseKey.wasPressed()) {
            handleDecrease(client);
        }
    }

    /**
     * Xử lý logic khi tăng giá trị với debouncing
     */
    protected void handleIncrease(MinecraftClient client) {
        int currentValue = getCurrentValue(client);

        // Nếu chưa có pending change, lưu giá trị gốc
        if (!hasPendingChange) {
            originalValue = currentValue;
            pendingValue = currentValue;
        }

        // Tính giá trị mới
        int newValue = Math.min(pendingValue + step, maxValue);

        if (newValue != pendingValue) {
            schedulePendingChange(client, newValue);
        } else {
            showMaxMessage(client);
        }
    }

    /**
     * Xử lý logic khi giảm giá trị với debouncing
     */
    protected void handleDecrease(MinecraftClient client) {
        int currentValue = getCurrentValue(client);

        // Nếu chưa có pending change, lưu giá trị gốc
        if (!hasPendingChange) {
            originalValue = currentValue;
            pendingValue = currentValue;
        }

        // Tính giá trị mới
        int newValue = Math.max(pendingValue - step, minValue);

        if (newValue != pendingValue) {
            schedulePendingChange(client, newValue);
        } else {
            showMinMessage(client);
        }
    }

    /**
     * Schedule pending change với 2 giây delay
     */
    private void schedulePendingChange(MinecraftClient client, int newValue) {
        pendingValue = newValue;
        hasPendingChange = true;

        // Hủy timer cũ nếu có
        if (debounceTimer != null) {
            debounceTimer.cancel();
        }

        // Hiển thị preview
        showPreviewMessage(client, newValue);

        // Tạo timer mới với delay từ config
        debounceTimer = new Timer();
        debounceTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                applyPendingChange(client);
            }
        }, ModConfig.DEBOUNCE_DELAY_MS);
    }

    /**
     * Apply pending change thực sự
     */
    private void applyPendingChange(MinecraftClient client) {
        if (!hasPendingChange) return;

        // Chỉ apply nếu giá trị thực sự thay đổi
        if (pendingValue != originalValue) {
            setValue(client, pendingValue);
            showChangeMessage(client, pendingValue);
        }

        // Reset pending state
        hasPendingChange = false;
        debounceTimer = null;
    }

    /**
     * Hiển thị preview message
     */
    private void showPreviewMessage(MinecraftClient client, int previewValue) {
        if (client.player != null) {
            int delaySeconds = ModConfig.DEBOUNCE_DELAY_MS / 1000;
            Text featureNameTranslated = Text.translatable(getFeatureNameKey());
            client.player.sendMessage(
                    Text.translatable(TranslationKeys.DISTANCE_PREVIEW, featureNameTranslated.getString(), previewValue, delaySeconds),
                    true
            );
        }
    }

    // Abstract methods - mỗi subclass phải implement

    /**
     * Lấy giá trị hiện tại của setting
     */
    protected abstract int getCurrentValue(MinecraftClient client);

    /**
     * Đặt giá trị mới cho setting
     */
    protected abstract void setValue(MinecraftClient client, int value);

    /**
     * Hiển thị message khi giá trị thay đổi
     */
    protected abstract void showChangeMessage(MinecraftClient client, int newValue);

    /**
     * Hiển thị message khi đạt giá trị tối đa
     */
    protected abstract void showMaxMessage(MinecraftClient client);

    /**
     * Hiển thị message khi đạt giá trị tối thiểu
     */
    protected abstract void showMinMessage(MinecraftClient client);

    // Getters

    public String getFeatureName() {
        return featureName;
    }

    public KeyBinding getIncreaseKey() {
        return increaseKey;
    }

    public KeyBinding getDecreaseKey() {
        return decreaseKey;
    }

    /**
     * Lấy translation key cho tên feature
     */
    protected abstract String getFeatureNameKey();

    /**
     * Helper method để tạo message với feature name được dịch
     */
    protected void sendFeatureMessage(MinecraftClient client, String messageKey, String messageTypeKey, int value) {
        if (client.player != null) {
            Text featureName = Text.translatable(getFeatureNameKey());
            Text messageType = Text.translatable(messageTypeKey);
            client.player.sendMessage(
                Text.translatable(messageKey, featureName.getString(), messageType.getString(), value),
                true
            );
        }
    }

    /**
     * Helper method để tạo message changed với feature name được dịch
     */
    protected void sendFeatureChangedMessage(MinecraftClient client, String messageKey, int value) {
        if (client.player != null) {
            Text featureName = Text.translatable(getFeatureNameKey());
            client.player.sendMessage(
                Text.translatable(messageKey, featureName.getString(), value),
                true
            );
        }
    }

    /**
     * Kiểm tra xem feature có được enable không
     */
    public abstract boolean isEnabled();
}